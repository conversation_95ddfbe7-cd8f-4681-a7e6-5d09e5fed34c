"""
GPU Isolation and Memory Management for Multi-GPU llama.cpp Setup

This module provides utilities to properly isolate GPU access and prevent
memory corruption when running multiple llama.cpp instances on different GPUs.
"""

import os
import logging
import threading
import time
from typing import Optional, Dict, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)

# Global locks for GPU access
_gpu_locks = {
    0: threading.Lock(),  # RTX 4070
    1: threading.Lock(),  # RX 6650XT
}

# Track active models per GPU
_active_models = {
    0: None,  # RTX 4070
    1: None,  # RX 6650XT
}

class GPUContext:
    """Context manager for isolated GPU access"""
    
    def __init__(self, gpu_id: int, model_name: str):
        self.gpu_id = gpu_id
        self.model_name = model_name
        self.lock = _gpu_locks.get(gpu_id)
        if not self.lock:
            raise ValueError(f"Invalid GPU ID: {gpu_id}")
    
    def __enter__(self):
        logger.debug(f"Acquiring GPU {self.gpu_id} lock for {self.model_name}")
        self.lock.acquire()
        
        # Set environment for this specific GPU
        if self.gpu_id == 0:
            # RTX 4070 (NVIDIA)
            os.environ["GGML_VK_DEVICE"] = "0"
            os.environ["GGML_VK_VISIBLE_DEVICES"] = "0"
        elif self.gpu_id == 1:
            # RX 6650XT (AMD)
            os.environ["GGML_VK_DEVICE"] = "1"
            os.environ["GGML_VK_VISIBLE_DEVICES"] = "1"
        
        logger.debug(f"GPU {self.gpu_id} environment configured for {self.model_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Reset environment to show both GPUs
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"
        if "GGML_VK_DEVICE" in os.environ:
            del os.environ["GGML_VK_DEVICE"]
        
        logger.debug(f"Releasing GPU {self.gpu_id} lock for {self.model_name}")
        self.lock.release()

@contextmanager
def isolated_gpu_access(gpu_id: int, model_name: str):
    """Context manager for isolated GPU access"""
    with GPUContext(gpu_id, model_name):
        yield

def get_safe_llama_params(gpu_id: int, model_path: str, **kwargs) -> Dict[str, Any]:
    """
    Get safe llama.cpp parameters for the specified GPU to prevent memory conflicts.
    
    Args:
        gpu_id: GPU device ID (0 for RTX 4070, 1 for RX 6650XT)
        model_path: Path to the GGUF model file
        **kwargs: Additional parameters to override defaults
    
    Returns:
        Dictionary of safe parameters for Llama initialization
    """
    
    # Base safe parameters
    base_params = {
        "model_path": model_path,
        "verbose": False,
        "logits_all": False,
        "vocab_only": False,
        "mul_mat_q": True,
        "f16_kv": True,
    }
    
    if gpu_id == 0:
        # RTX 4070 (NVIDIA) - Main response model
        gpu_params = {
            "main_gpu": 0,
            "n_gpu_layers": -1,  # Offload all layers
            "use_mmap": True,    # More stable for main model
            "use_mlock": False,  # Prevent memory conflicts
            "n_batch": 512,      # Larger batch for performance
            "n_threads_batch": 8,
            "n_ctx": kwargs.get("n_ctx", 4096),
            "n_threads": kwargs.get("n_threads", -1),
        }
    elif gpu_id == 1:
        # RX 6650XT (AMD) - Decision/utility models
        gpu_params = {
            "main_gpu": 1,
            "n_gpu_layers": -1,  # Offload all layers
            "use_mmap": False,   # Disable for secondary models
            "use_mlock": False,  # Prevent memory conflicts
            "n_batch": 256,      # Smaller batch for stability
            "n_threads_batch": 4,
            "n_ctx": kwargs.get("n_ctx", 2048),  # Smaller context for utility models
            "n_threads": kwargs.get("n_threads", 4),
        }
    else:
        raise ValueError(f"Unsupported GPU ID: {gpu_id}")
    
    # Merge base params with GPU-specific params
    safe_params = {**base_params, **gpu_params}
    
    # Override with any user-provided parameters
    safe_params.update(kwargs)
    
    logger.info(f"Generated safe parameters for GPU {gpu_id}: {model_path}")
    logger.debug(f"Parameters: {safe_params}")
    
    return safe_params

def cleanup_gpu_resources():
    """Clean up GPU resources and reset environment"""
    logger.info("Cleaning up GPU resources...")
    
    # Reset environment variables
    os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"
    if "GGML_VK_DEVICE" in os.environ:
        del os.environ["GGML_VK_DEVICE"]
    
    # Clear active models tracking
    global _active_models
    _active_models = {0: None, 1: None}
    
    logger.info("GPU resources cleaned up")

def register_model(gpu_id: int, model_name: str):
    """Register a model as active on a specific GPU"""
    global _active_models
    _active_models[gpu_id] = model_name
    logger.info(f"Registered model '{model_name}' on GPU {gpu_id}")

def unregister_model(gpu_id: int):
    """Unregister a model from a specific GPU"""
    global _active_models
    old_model = _active_models.get(gpu_id)
    _active_models[gpu_id] = None
    if old_model:
        logger.info(f"Unregistered model '{old_model}' from GPU {gpu_id}")

def get_active_models() -> Dict[int, Optional[str]]:
    """Get currently active models per GPU"""
    return _active_models.copy()

def wait_for_gpu_ready(gpu_id: int, timeout: float = 30.0) -> bool:
    """
    Wait for a GPU to be ready for model loading.
    
    Args:
        gpu_id: GPU device ID
        timeout: Maximum time to wait in seconds
    
    Returns:
        True if GPU is ready, False if timeout
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        lock = _gpu_locks.get(gpu_id)
        if lock and lock.acquire(blocking=False):
            try:
                # GPU is available
                return True
            finally:
                lock.release()
        
        # Wait a bit before retrying
        time.sleep(0.1)
    
    logger.warning(f"Timeout waiting for GPU {gpu_id} to be ready")
    return False
