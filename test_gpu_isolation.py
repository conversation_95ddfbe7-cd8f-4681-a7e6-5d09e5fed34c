#!/usr/bin/env python3
"""
Test script to verify GPU isolation and memory management fixes.
This script tests the llama.cpp setup with proper GPU isolation.
"""

import os
import sys
import asyncio
import logging
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_gpu_isolation():
    """Test the GPU isolation system"""
    logger.info("Testing GPU isolation system...")
    
    try:
        from llm_response.gpu_isolation import (
            isolated_gpu_access, 
            get_safe_llama_params, 
            register_model,
            get_active_models,
            cleanup_gpu_resources
        )
        
        # Test parameter generation for both GPUs
        logger.info("Testing parameter generation for RTX 4070 (GPU 0)...")
        rtx_params = get_safe_llama_params(
            gpu_id=0,
            model_path="./models/test.gguf",
            n_ctx=2048
        )
        logger.info(f"RTX 4070 params: {rtx_params}")
        
        logger.info("Testing parameter generation for RX 6650XT (GPU 1)...")
        amd_params = get_safe_llama_params(
            gpu_id=1,
            model_path="./models/test.gguf",
            n_ctx=2048
        )
        logger.info(f"RX 6650XT params: {amd_params}")
        
        # Test GPU context isolation
        logger.info("Testing GPU context isolation...")
        with isolated_gpu_access(0, "test-rtx"):
            logger.info("Inside RTX 4070 context")
            register_model(0, "test-rtx")
            
        with isolated_gpu_access(1, "test-amd"):
            logger.info("Inside RX 6650XT context")
            register_model(1, "test-amd")
        
        # Check active models
        active = get_active_models()
        logger.info(f"Active models: {active}")
        
        # Cleanup
        cleanup_gpu_resources()
        logger.info("GPU isolation test completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"GPU isolation test failed: {e}", exc_info=True)
        return False

def test_brain_initialization():
    """Test the brain model initialization"""
    logger.info("Testing brain model initialization...")
    
    try:
        from llm_brain import get_gemma_cpp_client
        
        # Test brain client initialization
        logger.info("Initializing Gemma brain client...")
        client = get_gemma_cpp_client()
        
        if client:
            logger.info("✅ Brain client initialized successfully!")
            return True
        else:
            logger.error("❌ Brain client initialization failed!")
            return False
            
    except Exception as e:
        logger.error(f"Brain initialization test failed: {e}", exc_info=True)
        return False

def test_decision_model():
    """Test the decision model initialization"""
    logger.info("Testing decision model initialization...")
    
    try:
        from llm_response.decision import get_decision_model
        
        # Test decision model initialization
        logger.info("Initializing decision model...")
        model = get_decision_model()
        
        if model:
            logger.info("✅ Decision model initialized successfully!")
            return True
        else:
            logger.error("❌ Decision model initialization failed!")
            return False
            
    except Exception as e:
        logger.error(f"Decision model test failed: {e}", exc_info=True)
        return False

def test_main_response_model():
    """Test the main response model initialization"""
    logger.info("Testing main response model initialization...")
    
    try:
        import llm_response
        
        # Test main response model initialization
        logger.info("Initializing main response model...")
        llm_response.initialize_ollama()  # This actually initializes llama.cpp now
        
        logger.info("✅ Main response model initialized successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Main response model test failed: {e}", exc_info=True)
        return False

async def test_brain_inference():
    """Test brain inference to check for access violations"""
    logger.info("Testing brain inference...")
    
    try:
        from llm_brain import get_brain_decision
        
        # Test a simple brain decision
        test_prompt = "User 'TestUser' said: 'Hey Luna, how are you?'"
        logger.info(f"Testing with prompt: {test_prompt}")
        
        result = await get_brain_decision(test_prompt)
        logger.info(f"Brain decision result: {result}")
        
        if result:
            logger.info("✅ Brain inference test passed!")
            return True
        else:
            logger.warning("⚠️ Brain inference returned empty result")
            return False
            
    except Exception as e:
        logger.error(f"Brain inference test failed: {e}", exc_info=True)
        return False

async def main():
    """Run all tests"""
    logger.info("Starting GPU isolation and llama.cpp tests...")
    
    tests = [
        ("GPU Isolation", test_gpu_isolation),
        ("Brain Initialization", test_brain_initialization),
        ("Decision Model", test_decision_model),
        ("Main Response Model", test_main_response_model),
        ("Brain Inference", test_brain_inference),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}", exc_info=True)
            results[test_name] = False
        
        # Small delay between tests
        await asyncio.sleep(1)
    
    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! GPU isolation is working correctly.")
        return True
    else:
        logger.error("⚠️ Some tests failed. Check the logs above for details.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
